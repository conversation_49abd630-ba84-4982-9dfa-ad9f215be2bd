//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import 'package:nylo_framework/nylo_framework.dart';

/// Enhanced Form Validation Service with consistent feedback
class FormValidationService {
  static final FormValidationService _instance = FormValidationService._internal();
  factory FormValidationService() => _instance;
  FormValidationService._internal();

  static FormValidationService get instance => _instance;

  /// Validate email with enhanced feedback
  static ValidationResult validateEmail(String email, {String? fieldName}) {
    fieldName ??= 'البريد الإلكتروني';
    
    if (email.isEmpty) {
      return ValidationResult(
        isValid: false,
        message: '$fieldName مطلوب',
        severity: ValidationSeverity.error,
      );
    }

    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );

    if (!emailRegex.hasMatch(email)) {
      return ValidationResult(
        isValid: false,
        message: 'يرجى إدخال $fieldName صحيح',
        severity: ValidationSeverity.error,
      );
    }

    return ValidationResult(isValid: true);
  }

  /// Validate password with strength requirements
  static ValidationResult validatePassword(String password, {bool requireStrong = false}) {
    if (password.isEmpty) {
      return ValidationResult(
        isValid: false,
        message: 'كلمة المرور مطلوبة',
        severity: ValidationSeverity.error,
      );
    }

    if (password.length < 6) {
      return ValidationResult(
        isValid: false,
        message: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل',
        severity: ValidationSeverity.error,
      );
    }

    if (requireStrong) {
      if (!password.contains(RegExp(r'[A-Z]'))) {
        return ValidationResult(
          isValid: false,
          message: 'كلمة المرور يجب أن تحتوي على حرف كبير',
          severity: ValidationSeverity.warning,
        );
      }

      if (!password.contains(RegExp(r'[0-9]'))) {
        return ValidationResult(
          isValid: false,
          message: 'كلمة المرور يجب أن تحتوي على رقم',
          severity: ValidationSeverity.warning,
        );
      }
    }

    return ValidationResult(isValid: true);
  }

  /// Validate phone number (Libya format)
  static ValidationResult validatePhone(String phone) {
    if (phone.isEmpty) {
      return ValidationResult(
        isValid: false,
        message: 'رقم الهاتف مطلوب',
        severity: ValidationSeverity.error,
      );
    }

    // Remove spaces and formatting
    String cleanPhone = phone.replaceAll(RegExp(r'[\s\-\(\)]'), '');
    
    // Libya phone number validation (+218 or 0)
    if (!cleanPhone.startsWith('+218') && !cleanPhone.startsWith('218') && !cleanPhone.startsWith('0')) {
      return ValidationResult(
        isValid: false,
        message: 'رقم الهاتف يجب أن يبدأ بـ +218 أو 0',
        severity: ValidationSeverity.error,
      );
    }

    // Check length (Libya mobile numbers are typically 10 digits after country code)
    if (cleanPhone.length < 10) {
      return ValidationResult(
        isValid: false,
        message: 'رقم الهاتف غير صحيح',
        severity: ValidationSeverity.error,
      );
    }

    return ValidationResult(isValid: true);
  }

  /// Validate required field
  static ValidationResult validateRequired(String value, String fieldName) {
    if (value.trim().isEmpty) {
      return ValidationResult(
        isValid: false,
        message: '$fieldName مطلوب',
        severity: ValidationSeverity.error,
      );
    }
    return ValidationResult(isValid: true);
  }

  /// Validate name (Arabic and English)
  static ValidationResult validateName(String name, String fieldName) {
    if (name.trim().isEmpty) {
      return ValidationResult(
        isValid: false,
        message: '$fieldName مطلوب',
        severity: ValidationSeverity.error,
      );
    }

    if (name.trim().length < 2) {
      return ValidationResult(
        isValid: false,
        message: '$fieldName يجب أن يكون حرفين على الأقل',
        severity: ValidationSeverity.error,
      );
    }

    return ValidationResult(isValid: true);
  }

  /// Show validation feedback to user
  static void showValidationFeedback(BuildContext context, ValidationResult result) {
    if (!result.isValid && result.message != null) {
      ToastNotificationStyleType style;
      switch (result.severity) {
        case ValidationSeverity.error:
          style = ToastNotificationStyleType.danger;
          break;
        case ValidationSeverity.warning:
          style = ToastNotificationStyleType.warning;
          break;
        case ValidationSeverity.info:
          style = ToastNotificationStyleType.info;
          break;
      }

      showToast(
        title: "تحقق من البيانات",
        description: result.message!,
        style: style,
      );
    }
  }

  /// Validate multiple fields at once
  static List<ValidationResult> validateFields(Map<String, dynamic> fields) {
    List<ValidationResult> results = [];
    
    fields.forEach((fieldName, value) {
      if (fieldName.toLowerCase().contains('email')) {
        results.add(validateEmail(value.toString(), fieldName: fieldName));
      } else if (fieldName.toLowerCase().contains('password')) {
        results.add(validatePassword(value.toString()));
      } else if (fieldName.toLowerCase().contains('phone')) {
        results.add(validatePhone(value.toString()));
      } else {
        results.add(validateRequired(value.toString(), fieldName));
      }
    });

    return results;
  }

  /// Check if all validations passed
  static bool allValid(List<ValidationResult> results) {
    return results.every((result) => result.isValid);
  }
}

/// Validation result class
class ValidationResult {
  final bool isValid;
  final String? message;
  final ValidationSeverity severity;

  ValidationResult({
    required this.isValid,
    this.message,
    this.severity = ValidationSeverity.error,
  });
}

/// Validation severity levels
enum ValidationSeverity {
  error,
  warning,
  info,
}
