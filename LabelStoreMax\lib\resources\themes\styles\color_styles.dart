import 'package:nylo_framework/nylo_framework.dart';

/// Interface for your base styles.
/// Add more styles here and then implement in
/// light_theme_colors.dart and dark_theme_colors.dart.
abstract class ColorStyles extends BaseColorStyles {
  // general
  @override
  Color get background;
  Color get backgroundContainer;
  @override
  Color get content;
  @override
  Color get primaryAccent;

  @override
  Color get surfaceBackground;
  @override
  Color get surfaceContent;

  // app bar
  @override
  Color get appBarBackground;
  @override
  Color get appBarPrimaryContent;

  // buttons
  @override
  Color get buttonBackground;
  @override
  Color get buttonContent;

  // bottom tab bar
  @override
  Color get bottomTabBarBackground;

  // bottom tab bar - icons
  @override
  Color get bottomTabBarIconSelected;
  @override
  Color get bottomTabBarIconUnselected;

  // bottom tab bar - label
  @override
  Color get bottomTabBarLabelUnselected;
  @override
  Color get bottomTabBarLabelSelected;

  Color get inputPrimaryContent;

  // === STANDARDIZED BRAND COLORS ===

  // Primary Brand Colors
  Color get brandPrimary; // Main brand color #B76E79
  Color get brandSecondary; // Secondary brand color #F4C2C2
  Color get brandAccent; // Accent color for highlights

  // UI State Colors
  Color get successColor; // Success states
  Color get warningColor; // Warning states
  Color get errorColor; // Error states
  Color get infoColor; // Information states

  // Text Colors
  Color get textPrimary; // Primary text color
  Color get textSecondary; // Secondary text color
  Color get textMuted; // Muted text color
  Color get textOnPrimary; // Text on primary background

  // Surface Colors
  Color get surfacePrimary; // Primary surface
  Color get surfaceSecondary; // Secondary surface
  Color get surfaceElevated; // Elevated surface (cards)
  Color get surfaceOverlay; // Overlay surface

  // Border Colors
  Color get borderPrimary; // Primary border color
  Color get borderSecondary; // Secondary border color
  Color get borderFocus; // Focus border color

  // Shadow Colors
  Color get shadowLight; // Light shadow
  Color get shadowMedium; // Medium shadow
  Color get shadowDark; // Dark shadow
}
