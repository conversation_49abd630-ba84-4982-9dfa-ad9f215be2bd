<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="@dimen/stripe_list_row_height"
    android:orientation="horizontal"
    android:paddingStart="16dp"
    android:paddingEnd="12dp">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/icon"
        android:layout_width="@dimen/stripe_masked_card_icon_width"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical|start"
        app:srcCompat="@drawable/stripe_ic_bank_generic" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/name"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical|start"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_marginStart="8dp"
        android:textAppearance="@android:style/TextAppearance.DeviceDefault.Medium" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/check_icon"
        android:layout_width="@dimen/stripe_masked_card_icon_width"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        app:srcCompat="@drawable/stripe_ic_checkmark" />

</LinearLayout>
