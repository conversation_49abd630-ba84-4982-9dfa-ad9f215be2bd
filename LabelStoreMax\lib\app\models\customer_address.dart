//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import '/app/models/customer_country.dart';
import '/app/models/default_shipping.dart';
import '/bootstrap/helpers.dart';
import 'package:wp_json_api/models/wp_meta_meta.dart';

class CustomerAddress {
  String? firstName;
  String? lastName;
  String? addressLine;
  String? city;
  String? postalCode;
  String? emailAddress;
  String? phoneNumber;
  CustomerCountry? customerCountry;
  double? latitude;
  double? longitude;
  String? addressId; // Unique identifier for address management
  String? addressLabel; // "Home", "Work", "Other"
  bool? isDefault; // Default address flag

  CustomerAddress(
      {this.firstName,
      this.lastName,
      this.addressLine,
      this.city,
      this.postalCode,
      this.emailAddress,
      this.phoneNumber,
      this.customerCountry,
      this.latitude,
      this.longitude,
      this.addressId,
      this.addressLabel,
      this.isDefault});

  void initAddress() {
    firstName = "";
    lastName = "";
    addressLine = "";
    city = "";
    postalCode = "";
    customerCountry = CustomerCountry();
    emailAddress = "";
    phoneNumber = "";
    latitude = null;
    longitude = null;
    addressId = null;
    addressLabel = null;
    isDefault = false;
  }

  bool hasMissingFields() =>
      ((firstName?.isEmpty ?? true) ||
          (lastName?.isEmpty ?? true) ||
          (addressLine?.isEmpty ?? true) ||
          (city?.isEmpty ?? true) ||
          (postalCode?.isEmpty ?? true)) ||
      ((customerCountry?.hasState() ?? false) == true
          ? (customerCountry?.state?.name ?? "").isEmpty
          : false);

  String addressFull() {
    List<String?> tmpArrAddress = [];
    if (addressLine != null && addressLine != "") {
      tmpArrAddress.add(addressLine);
    }
    if (city != null && city != "") {
      tmpArrAddress.add(city);
    }
    if (postalCode != null && postalCode != "") {
      tmpArrAddress.add(postalCode);
    }
    if (customerCountry != null && customerCountry?.state?.name != null) {
      tmpArrAddress.add(customerCountry?.state?.name);
    }
    if (customerCountry != null && customerCountry?.name != null) {
      tmpArrAddress.add(customerCountry!.name);
    }
    return tmpArrAddress.join(", ");
  }

  String nameFull() {
    List<String?> tmpArrName = [];
    if (firstName != null && firstName!.isNotEmpty) {
      tmpArrName.add(firstName);
    }
    if (lastName != null && lastName!.isNotEmpty) {
      tmpArrName.add(lastName);
    }
    return tmpArrName.join(", ");
  }

  CustomerAddress.fromJson(Map<String, dynamic> json) {
    firstName = json['first_name'];
    lastName = json['last_name'];
    addressLine = json['address_line'];
    city = json['city'];
    postalCode = json['postal_code'];
    if (json['phone_number'] != null) {
      phoneNumber = json['phone_number'];
    }
    customerCountry = CustomerCountry.fromJson(json['customer_country']);
    emailAddress = json['email_address'];
    if (json['latitude'] != null) {
      latitude = json['latitude'] is double ? json['latitude'] : double.tryParse(json['latitude'].toString());
    }
    if (json['longitude'] != null) {
      longitude = json['longitude'] is double ? json['longitude'] : double.tryParse(json['longitude'].toString());
    }
    addressId = json['address_id'];
    addressLabel = json['address_label'];
    isDefault = json['is_default'] ?? false;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['first_name'] = firstName;
    data['last_name'] = lastName;
    data['address_line'] = addressLine;
    // INVESTIGATION ZONE 1 - Task 1.1: Ensure address_2 is never null (WooCommerce API requirement)
    data['address_2'] = ''; // Always empty string, never null for WooCommerce API compliance
    data['city'] = city;
    data['postal_code'] = postalCode;
    // FINAL INVESTIGATION - Task 1.1: Ensure state is never null (WooCommerce API requirement)
    data['state'] = customerCountry?.state?.name ?? ''; // Always empty string, never null for WooCommerce API compliance
    data['country'] = customerCountry!.name;
    if (phoneNumber != null && phoneNumber != "") {
      data['phone_number'] = phoneNumber;
    }
    data['address_id'] = addressId;
    data['address_label'] = addressLabel;
    data['is_default'] = isDefault ?? false;
    data['email_address'] = emailAddress;
    data['customer_country'] = null;
    if (customerCountry != null) {
      data['customer_country'] = customerCountry!.toJson();
    }
    if (latitude != null) {
      data['latitude'] = latitude;
    }
    if (longitude != null) {
      data['longitude'] = longitude;
    }
    return data;
  }

  fromWpMetaData(Map<String, dynamic> data) async {
    if (data.containsKey('first_name')) {
      firstName = data['first_name'];
    }

    if (data.containsKey('last_name')) {
      lastName = data['last_name'];
    }

    if (data.containsKey('address_1')) {
      addressLine = data['address_1'];
    }

    if (data.containsKey('city')) {
      city = data['city'];
    }

    if (data.containsKey('postcode')) {
      postalCode = data['postcode'];
    }

    if (data.containsKey('email')) {
      emailAddress = data['email'];
    }

    if (data.containsKey('phone')) {
      phoneNumber = data['phone'];
    }

    if (data.containsKey('country')) {
      DefaultShipping? defaultShipping =
          await findCountryMetaForShipping(data['country']);
      if (defaultShipping == null) {
        return;
      }
      customerCountry = CustomerCountry.fromWpMeta(data, defaultShipping);
    }
  }

  List<WpMetaData> toUserMetaDataItem(String type) {
    return [
      WpMetaData(key: "${type}_first_name", value: firstName),
      WpMetaData(key: "${type}_last_name", value: lastName),
      WpMetaData(key: "${type}_address_1", value: addressLine),
      WpMetaData(key: "${type}_city", value: city),
      WpMetaData(key: "${type}_postcode", value: postalCode),
      WpMetaData(key: "${type}_phone", value: phoneNumber),
      if (type != "shipping")
        WpMetaData(key: "${type}_email", value: emailAddress),
      WpMetaData(key: "${type}_country", value: customerCountry?.countryCode),
      WpMetaData(
          key: "${type}_state",
          value: customerCountry?.state?.code
              ?.replaceAll("${customerCountry?.countryCode}:", "")),
    ];
  }
}
