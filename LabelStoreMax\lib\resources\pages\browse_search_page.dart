//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import '/resources/widgets/product_item_container_widget.dart';
import '/resources/pages/product_detail_page.dart';
import '/resources/widgets/safearea_widget.dart';
import 'package:nylo_framework/nylo_framework.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';
import '/app/models/woocommerce_wrappers/my_product.dart';
import '/app/services/woocommerce_service.dart';

class BrowseSearchPage extends NyStatefulWidget {
  static RouteView path = ("/product-search", (_) => BrowseSearchPage());

  BrowseSearchPage({super.key}) : super(child: () => _BrowseSearchState());
}

class _BrowseSearchState extends NyPage<BrowseSearchPage> {
  String? _search;

  @override
  get init => () {
        _search = widget.controller.data();
      };

  Future<List<MyProduct>> _getSearchResults() async {
    if (_search == null || _search!.isEmpty) {
      return <MyProduct>[];
    }

    try {
      print('🔍 Searching for products: "$_search"');
      // Implement product search with WooCommerce API
      return await WooCommerceService().getProducts(
        perPage: 100,
        search: _search,
        page: 1,
        status: WooFilterStatus.publish,
        stockStatus: WooProductStockStatus.instock,
      );
    } catch (e) {
      print('❌ Search failed: $e');
      return <MyProduct>[];
    }
  }

  @override
  Widget view(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Text(trans("Search results for"),
                style: Theme.of(context).textTheme.titleMedium),
            afterNotNull(_search,
                child: () => Text("\"${_search!}\""),
                loading: CupertinoActivityIndicator())
          ],
        ),
        centerTitle: true,
      ),
      body: SafeAreaWidget(
        child: RefreshIndicator(
          onRefresh: () async {
            // Refresh search results
            setState(() {});
          },
          child: CustomScrollView(
            slivers: [
              FutureBuilder<List<MyProduct>>(
                future: _getSearchResults(),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return SliverToBoxAdapter(
                      child: _buildSearchLoadingWidget(),
                    );
                  }

                  if (snapshot.hasError) {
                    return SliverToBoxAdapter(
                      child: Container(
                        height: 200,
                        child: Center(child: Text('Error searching products: ${snapshot.error}')),
                      ),
                    );
                  }

                  if (snapshot.hasData && snapshot.data != null && snapshot.data!.isNotEmpty) {
                    List<MyProduct> products = snapshot.data!;

                    return SliverGrid(
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio: 0.9,
                        crossAxisSpacing: 8,
                        mainAxisSpacing: 8,
                      ),
                      delegate: SliverChildBuilderDelegate(
                        (context, index) {
                          return ProductItemContainer(
                            product: products[index],
                            onTap: () {
                              routeTo(ProductDetailPage.path, data: products[index]);
                            },
                          );
                        },
                        childCount: products.length,
                      ),
                    );
                  }

                  // Handle null or empty data
                  return SliverToBoxAdapter(
                    child: _buildNoResultsState(context),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSearchLoadingWidget() {
    return Center(
      child: Container(
        width: 120,
        height: 120,
        child: Lottie.asset(
          'public/animations/search_loading.json',
          width: 120,
          height: 120,
          fit: BoxFit.contain,
          repeat: true,
          errorBuilder: (context, error, stackTrace) {
            // Fallback to default loading indicator if Lottie fails
            return CupertinoActivityIndicator();
          },
        ),
      ),
    );
  }

  Widget _buildNoResultsState(BuildContext context) {
    bool isDark = Theme.of(context).brightness == Brightness.dark;

    return Center(
      child: Padding(
        padding: EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // No Results Illustration
            Container(
              width: 150,
              height: 150,
              decoration: BoxDecoration(
                color: const Color(0xFFB76E79).withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Icon(
                    Icons.search,
                    size: 80,
                    color: const Color(0xFFB76E79).withValues(alpha: 0.3),
                  ),
                  Positioned(
                    top: 30,
                    right: 30,
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 24),

            // Main Message
            Text(
              'لم يتم العثور على نتائج',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: isDark ? Colors.white : const Color(0xFF2E3A59),
                  ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 12),

            // Secondary Message
            Text(
              'لم نتمكن من العثور على منتجات تطابق بحثك\nجرب استخدام كلمات مختلفة أو أقل تحديداً',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: isDark ? Colors.grey[400] : Colors.grey[600],
                    height: 1.5,
                  ),
              textAlign: TextAlign.center,
            ),

            SizedBox(height: 32),

            // Search Again Button
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4285F4),
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 2,
              ),
              child: Text(
                'البحث مرة أخرى',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
