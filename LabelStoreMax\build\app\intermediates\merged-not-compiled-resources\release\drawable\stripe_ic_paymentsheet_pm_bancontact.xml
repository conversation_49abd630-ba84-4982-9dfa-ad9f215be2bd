<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="27dp"
    android:height="16dp"
    android:viewportWidth="27"
    android:viewportHeight="16">
  <path
      android:pathData="M13.9998,9.0228C12.2165,11.3927 10.0615,14.0457 5.9658,14.0457H1C0.4477,14.0457 0,13.5979 0,13.0457V8.0228C0,7.4705 0.4477,7.0228 1,7.0228H13.0002C14.7835,4.6529 16.9385,2 21.0342,2H26C26.5523,2 27,2.4477 27,3V8.0228C27,8.5751 26.5523,9.0228 26,9.0228H13.9998Z"
      android:fillColor="#ffffff"/>
  <path
      android:pathData="M5.9658,13.0457C9.7329,13.0457 11.6164,10.5342 13.5,8.0228H1V13.0457H5.9658Z">
    <aapt:attr name="android:fillColor">
      <gradient
          android:startY="10.7661"
          android:startX="3.52836"
          android:endY="7.36967"
          android:endX="12.6726"
          android:type="linear">
        <item android:offset="0" android:color="#FF005AB9"/>
        <item android:offset="1" android:color="#FF1E3764"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M21.0342,3C17.2671,3 15.3836,5.5114 13.5,8.0228H26V3H21.0342Z">
    <aapt:attr name="android:fillColor">
      <gradient
          android:startY="8.45847"
          android:startX="14.2568"
          android:endY="5.14587"
          android:endX="23.9662"
          android:type="linear">
        <item android:offset="0" android:color="#FFFBA900"/>
        <item android:offset="1" android:color="#FFFFD800"/>
      </gradient>
    </aapt:attr>
  </path>
</vector>
