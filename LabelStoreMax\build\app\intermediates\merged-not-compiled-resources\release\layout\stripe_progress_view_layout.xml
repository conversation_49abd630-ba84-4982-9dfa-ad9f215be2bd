<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?android:attr/colorBackground"
    android:gravity="center"
    android:orientation="vertical"
    android:paddingTop="30dp"
    android:paddingBottom="30dp">

    <ImageView
        android:id="@+id/brand_logo"
        android:layout_width="96dp"
        android:layout_height="63dp"
        android:layout_marginHorizontal="40dp"
        android:layout_marginBottom="36dp"
        android:contentDescription="@null"
        android:visibility="gone"
        tools:src="@drawable/stripe_3ds2_ic_visa"
        tools:visibility="visible" />

    <com.google.android.material.progressindicator.CircularProgressIndicator
        android:id="@+id/progress_bar"
        style="@style/Widget.MaterialComponents.CircularProgressIndicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:indeterminate="true" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/stripe_3ds2_processing"
        android:textSize="14sp" />

</LinearLayout>
