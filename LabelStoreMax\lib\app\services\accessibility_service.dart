//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import 'package:flutter/semantics.dart';

/// Accessibility Enhancement Service for better app usability
class AccessibilityService {
  static final AccessibilityService _instance = AccessibilityService._internal();
  factory AccessibilityService() => _instance;
  AccessibilityService._internal();

  static AccessibilityService get instance => _instance;

  // Minimum touch target size (44x44 dp as per Material Design)
  static const double minTouchTargetSize = 44.0;
  
  // Recommended touch target size for better accessibility
  static const double recommendedTouchTargetSize = 48.0;

  /// Wrap widget with proper touch target size
  static Widget ensureTouchTarget(Widget child, {
    double? width,
    double? height,
    VoidCallback? onTap,
    String? semanticLabel,
    String? semanticHint,
  }) {
    return Semantics(
      label: semanticLabel,
      hint: semanticHint,
      button: onTap != null,
      child: SizedBox(
        width: width ?? recommendedTouchTargetSize,
        height: height ?? recommendedTouchTargetSize,
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(8),
            child: Center(child: child),
          ),
        ),
      ),
    );
  }

  /// Create accessible button with proper semantics
  static Widget accessibleButton({
    required Widget child,
    required VoidCallback onPressed,
    required String semanticLabel,
    String? semanticHint,
    bool enabled = true,
    double? width,
    double? height,
  }) {
    return Semantics(
      label: semanticLabel,
      hint: semanticHint,
      button: true,
      enabled: enabled,
      child: SizedBox(
        width: width ?? recommendedTouchTargetSize,
        height: height ?? recommendedTouchTargetSize,
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: enabled ? onPressed : null,
            borderRadius: BorderRadius.circular(8),
            child: Center(child: child),
          ),
        ),
      ),
    );
  }

  /// Create accessible text field with proper semantics
  static Widget accessibleTextField({
    required TextEditingController controller,
    required String label,
    String? hint,
    String? semanticLabel,
    bool obscureText = false,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    void Function(String)? onChanged,
    bool enabled = true,
  }) {
    return Semantics(
      label: semanticLabel ?? label,
      hint: hint,
      textField: true,
      enabled: enabled,
      child: TextFormField(
        controller: controller,
        obscureText: obscureText,
        keyboardType: keyboardType,
        validator: validator,
        onChanged: onChanged,
        enabled: enabled,
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          contentPadding: EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
      ),
    );
  }

  /// Create accessible image with proper semantics
  static Widget accessibleImage({
    required Widget image,
    required String semanticLabel,
    String? semanticHint,
    VoidCallback? onTap,
  }) {
    return Semantics(
      label: semanticLabel,
      hint: semanticHint,
      image: true,
      button: onTap != null,
      child: onTap != null
          ? GestureDetector(
              onTap: onTap,
              child: image,
            )
          : image,
    );
  }

  /// Create accessible list item with proper semantics
  static Widget accessibleListItem({
    required Widget child,
    required String semanticLabel,
    String? semanticHint,
    VoidCallback? onTap,
    bool selected = false,
  }) {
    return Semantics(
      label: semanticLabel,
      hint: semanticHint,
      button: onTap != null,
      selected: selected,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          child: Container(
            constraints: BoxConstraints(
              minHeight: recommendedTouchTargetSize,
            ),
            child: child,
          ),
        ),
      ),
    );
  }

  /// Create accessible navigation item
  static Widget accessibleNavItem({
    required Widget icon,
    required String label,
    required VoidCallback onTap,
    bool selected = false,
    String? semanticHint,
  }) {
    return Semantics(
      label: label,
      hint: semanticHint ?? (selected ? 'محدد حالياً' : 'اضغط للانتقال'),
      button: true,
      selected: selected,
      child: SizedBox(
        width: recommendedTouchTargetSize,
        height: recommendedTouchTargetSize,
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(8),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                icon,
                if (label.isNotEmpty)
                  Text(
                    label,
                    style: TextStyle(fontSize: 10),
                    textAlign: TextAlign.center,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Create accessible card with proper semantics
  static Widget accessibleCard({
    required Widget child,
    required String semanticLabel,
    String? semanticHint,
    VoidCallback? onTap,
  }) {
    return Semantics(
      label: semanticLabel,
      hint: semanticHint,
      button: onTap != null,
      child: Card(
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            constraints: BoxConstraints(
              minHeight: recommendedTouchTargetSize,
            ),
            child: child,
          ),
        ),
      ),
    );
  }

  /// Announce message to screen readers
  static void announceMessage(String message) {
    SemanticsService.announce(message, TextDirection.rtl);
  }

  /// Focus management helper
  static void requestFocus(BuildContext context, FocusNode focusNode) {
    FocusScope.of(context).requestFocus(focusNode);
  }

  /// Clear focus helper
  static void clearFocus(BuildContext context) {
    FocusScope.of(context).requestFocus(FocusNode());
  }

  /// Check if device has accessibility features enabled
  static bool isAccessibilityEnabled(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return mediaQuery.accessibleNavigation || 
           mediaQuery.boldText ||
           mediaQuery.highContrast;
  }

  /// Get appropriate text scale for accessibility
  static double getAccessibleTextScale(BuildContext context) {
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    // Ensure text doesn't become too large and break layouts
    return textScaleFactor.clamp(0.8, 2.0);
  }

  /// Create accessible loading indicator
  static Widget accessibleLoadingIndicator({
    String? semanticLabel,
  }) {
    return Semantics(
      label: semanticLabel ?? 'جاري التحميل',
      child: CircularProgressIndicator(),
    );
  }
}
