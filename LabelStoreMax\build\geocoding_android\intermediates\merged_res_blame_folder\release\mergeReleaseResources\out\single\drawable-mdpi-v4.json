[{"merged": "com.baseflow.geocoding.geocoding_android-release-25:/drawable-mdpi-v4/notification_bg_low_pressed.9.png", "source": "com.baseflow.geocoding.geocoding_android-core-1.13.1-2:/drawable-mdpi-v4/notification_bg_low_pressed.9.png"}, {"merged": "com.baseflow.geocoding.geocoding_android-release-25:/drawable-mdpi-v4/notification_bg_low_normal.9.png", "source": "com.baseflow.geocoding.geocoding_android-core-1.13.1-2:/drawable-mdpi-v4/notification_bg_low_normal.9.png"}, {"merged": "com.baseflow.geocoding.geocoding_android-release-25:/drawable-mdpi-v4/ic_call_decline.png", "source": "com.baseflow.geocoding.geocoding_android-core-1.13.1-2:/drawable-mdpi-v4/ic_call_decline.png"}, {"merged": "com.baseflow.geocoding.geocoding_android-release-25:/drawable-mdpi-v4/ic_call_answer_low.png", "source": "com.baseflow.geocoding.geocoding_android-core-1.13.1-2:/drawable-mdpi-v4/ic_call_answer_low.png"}, {"merged": "com.baseflow.geocoding.geocoding_android-release-25:/drawable-mdpi-v4/notify_panel_notification_icon_bg.png", "source": "com.baseflow.geocoding.geocoding_android-core-1.13.1-2:/drawable-mdpi-v4/notify_panel_notification_icon_bg.png"}, {"merged": "com.baseflow.geocoding.geocoding_android-release-25:/drawable-mdpi-v4/notification_bg_normal.9.png", "source": "com.baseflow.geocoding.geocoding_android-core-1.13.1-2:/drawable-mdpi-v4/notification_bg_normal.9.png"}, {"merged": "com.baseflow.geocoding.geocoding_android-release-25:/drawable-mdpi-v4/ic_call_answer.png", "source": "com.baseflow.geocoding.geocoding_android-core-1.13.1-2:/drawable-mdpi-v4/ic_call_answer.png"}, {"merged": "com.baseflow.geocoding.geocoding_android-release-25:/drawable-mdpi-v4/notification_bg_normal_pressed.9.png", "source": "com.baseflow.geocoding.geocoding_android-core-1.13.1-2:/drawable-mdpi-v4/notification_bg_normal_pressed.9.png"}, {"merged": "com.baseflow.geocoding.geocoding_android-release-25:/drawable-mdpi-v4/ic_call_answer_video_low.png", "source": "com.baseflow.geocoding.geocoding_android-core-1.13.1-2:/drawable-mdpi-v4/ic_call_answer_video_low.png"}, {"merged": "com.baseflow.geocoding.geocoding_android-release-25:/drawable-mdpi-v4/ic_call_answer_video.png", "source": "com.baseflow.geocoding.geocoding_android-core-1.13.1-2:/drawable-mdpi-v4/ic_call_answer_video.png"}, {"merged": "com.baseflow.geocoding.geocoding_android-release-25:/drawable-mdpi-v4/ic_call_decline_low.png", "source": "com.baseflow.geocoding.geocoding_android-core-1.13.1-2:/drawable-mdpi-v4/ic_call_decline_low.png"}]