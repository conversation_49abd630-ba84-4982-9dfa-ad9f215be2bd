// Velvete Store
//
// Created by <PERSON><PERSON>.
// Copyright © 2025, <PERSON><PERSON>. All rights reserved.
//
// This software is proprietary and confidential.
// Unauthorized copying, redistribution, or use of this software, in whole or in part,
// is strictly prohibited without the express written permission of <PERSON><PERSON>.
//
// All intellectual property rights, including copyrights, patents, trademarks,
// and trade secrets, in and to the software are owned by <PERSON><PERSON>.
//
// THE SOFTWARE IS PROVIDED "AS IS" WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED,
// INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A
// PARTICULAR PURPOSE, AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
// COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES, OR OTHER LIABILITY,
// WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

import 'package:flutter/material.dart';

import '/app/models/billing_details.dart';
import '/app/models/checkout_session.dart';
import '/app/models/customer_address.dart';
import '/app/models/customer_country.dart';
import '/app/models/shipping_type.dart';
import '/app/models/libyan_city.dart';
import '/app/services/libyan_delivery_service.dart';
import '/app/services/woocommerce_service.dart';
import '/bootstrap/app_helper.dart';
import '/bootstrap/helpers.dart';
import '/resources/widgets/app_loader_widget.dart';
import '/resources/widgets/buttons.dart';
import '/resources/widgets/customer_address_input.dart';
import '/resources/widgets/safearea_widget.dart';
import '/resources/widgets/switch_address_tab.dart';
import '/resources/widgets/velvete_ui.dart';
import 'package:nylo_framework/nylo_framework.dart';
import 'package:validated/validated.dart' as validate;
import 'package:wp_json_api/models/responses/wp_user_info_response.dart';
import 'package:wp_json_api/wp_json_api.dart';


class CheckoutDetailsPage extends NyStatefulWidget {
  static RouteView path = ("/checkout-details", (_) => CheckoutDetailsPage());

  CheckoutDetailsPage({super.key})
      : super(child: () => _CheckoutDetailsPageState());
}

class _CheckoutDetailsPageState extends NyPage<CheckoutDetailsPage> {
  bool? _hasDifferentShippingAddress = false,
      valRememberDetails = true,
      _wpLoginEnabled;
  int activeTabIndex = 0;

  // TEXT CONTROLLERS
  final TextEditingController
      // billing
      _txtBillingFirstName = TextEditingController(),
      _txtBillingLastName = TextEditingController(),
      _txtBillingAddressLine = TextEditingController(),
      _txtBillingCity = TextEditingController(),
      _txtBillingPostalCode = TextEditingController(),
      _txtBillingEmailAddress = TextEditingController(),
      _txtBillingPhoneNumber = TextEditingController(),
      // shipping
      _txtShippingFirstName = TextEditingController(),
      _txtShippingLastName = TextEditingController(),
      _txtShippingAddressLine = TextEditingController(),
      _txtShippingCity = TextEditingController(),
      _txtShippingPostalCode = TextEditingController(),
      _txtShippingEmailAddress = TextEditingController();

  CustomerCountry? _billingCountry, _shippingCountry;

  Widget? activeTab;

  Widget tabShippingDetails() => CustomerAddressInput(
        txtControllerFirstName: _txtShippingFirstName,
        txtControllerLastName: _txtShippingLastName,
        txtControllerAddressLine: _txtShippingAddressLine,
        txtControllerCity: _txtShippingCity,
        txtControllerPostalCode: _txtShippingPostalCode,
        txtControllerEmailAddress: _txtShippingEmailAddress,
        customerCountry: _shippingCountry,
        onTapCountry: () => _navigateToSelectCountry(type: "shipping"),
      );

  Widget tabBillingDetails() => CustomerAddressInput(
        txtControllerFirstName: _txtBillingFirstName,
        txtControllerLastName: _txtBillingLastName,
        txtControllerAddressLine: _txtBillingAddressLine,
        txtControllerCity: _txtBillingCity,
        txtControllerPostalCode: _txtBillingPostalCode,
        txtControllerEmailAddress: _txtBillingEmailAddress,
        txtControllerPhoneNumber: _txtBillingPhoneNumber,
        customerCountry: _billingCountry,
        onTapCountry: () => _navigateToSelectCountry(type: "billing"),
      );

  @override
  get init => () async {
        super.init();

        _wpLoginEnabled = AppHelper.instance.appConfig?.wpLoginEnabled == 1;

        if (_wpLoginEnabled == true && (await WPJsonAPI.wpUserLoggedIn())) {
          await awaitData(perform: () async {
            await _fetchUserDetails();
          });
          return;
        }

        // Ensure checkout session is properly initialized
        if (CheckoutSession.getInstance.billingDetails == null) {
          CheckoutSession.getInstance.initSession();
        }

        if (CheckoutSession.getInstance.billingDetails!.billingAddress ==
            null) {
          CheckoutSession.getInstance.billingDetails!.initSession();
          CheckoutSession.getInstance.billingDetails!.shippingAddress!
              .initAddress();
          CheckoutSession.getInstance.billingDetails!.billingAddress
              ?.initAddress();
        }
        BillingDetails billingDetails =
            CheckoutSession.getInstance.billingDetails!;
        _setFieldsFromCustomerAddress(billingDetails.billingAddress,
            type: "billing");
        _setFieldsFromCustomerAddress(billingDetails.shippingAddress,
            type: "shipping");

        _hasDifferentShippingAddress =
            CheckoutSession.getInstance.shipToDifferentAddress;
        valRememberDetails = billingDetails.rememberDetails ?? true;
        if (valRememberDetails == true) {
          await _setCustomersDetailsFromRemember();
          _setDefaultLibyaCountry();
          return;
        }
        _setDefaultLibyaCountry();
        setState(() {});
      };

  /// Set Libya as default country for both billing and shipping
  void _setDefaultLibyaCountry() {
    // Create Libya country object
    final libyaCountry = CustomerCountry(
      countryCode: 'LY',
      name: 'Libya',
    );

    // Always set Libya for both billing and shipping to ensure consistency
    _billingCountry = libyaCountry;
    _shippingCountry = libyaCountry;
    print('✅ Enforced Libya as country for both billing and shipping');

    // Ensure CheckoutSession is initialized with Libya country
    if (CheckoutSession.getInstance.billingDetails?.billingAddress != null) {
      CheckoutSession.getInstance.billingDetails!.billingAddress!.customerCountry = libyaCountry;
    }
    if (CheckoutSession.getInstance.billingDetails?.shippingAddress != null) {
      CheckoutSession.getInstance.billingDetails!.shippingAddress!.customerCountry = libyaCountry;
    }
  }

  _setCustomersDetailsFromRemember() async {
    CustomerAddress? sfCustomerBillingAddress =
        await CheckoutSession.getInstance.getBillingAddress();
    _setFieldsFromCustomerAddress(sfCustomerBillingAddress, type: "billing");

    CustomerAddress? sfCustomerShippingAddress =
        await CheckoutSession.getInstance.getShippingAddress();
    _setFieldsFromCustomerAddress(sfCustomerShippingAddress, type: "shipping");
    setState(() {});
  }

  _setFieldsFromCustomerAddress(CustomerAddress? customerAddress,
      {required String type}) {
    assert(type != "");
    if (customerAddress == null) {
      return;
    }
    _setFields(
      firstName: customerAddress.firstName,
      lastName: customerAddress.lastName,
      addressLine: customerAddress.addressLine,
      city: customerAddress.city,
      postalCode: customerAddress.postalCode,
      emailAddress: customerAddress.emailAddress,
      phoneNumber: customerAddress.phoneNumber,
      customerCountry: customerAddress.customerCountry,
      type: type,
    );
  }

  _setFields(
      {required String? firstName,
      required String? lastName,
      required String? addressLine,
      required String? city,
      required String? postalCode,
      required String? emailAddress,
      required String? phoneNumber,
      required CustomerCountry? customerCountry,
      String? type}) {
    if (type == "billing") {
      _txtBillingFirstName.text = firstName ?? "";
      _txtBillingLastName.text = lastName ?? "";
      _txtBillingAddressLine.text = addressLine ?? "";
      _txtBillingCity.text = city ?? "";
      _txtBillingPostalCode.text = postalCode ?? "";
      _txtBillingPhoneNumber.text = phoneNumber ?? "";
      _txtBillingEmailAddress.text = emailAddress ?? "";
      _billingCountry = customerCountry;
    } else if (type == "shipping") {
      _txtShippingFirstName.text = firstName ?? "";
      _txtShippingLastName.text = lastName ?? "";
      _txtShippingAddressLine.text = addressLine ?? "";
      _txtShippingCity.text = city ?? "";
      _txtShippingPostalCode.text = postalCode ?? "";
      _txtShippingEmailAddress.text = emailAddress ?? "";
      _shippingCountry = customerCountry;
    }
  }

  @override
  Widget view(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        title: Text(
          trans("Billing & Shipping Details"),
        ),
        centerTitle: true,
      ),
      body: SafeAreaWidget(
        child: (isLoading() || isLocked('load_shipping_info'))
            ? AppLoaderWidget()
            : GestureDetector(
                onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
                child: Column(
                  children: <Widget>[
                    // Tab switcher section (only show when different shipping address is enabled)
                    if (_hasDifferentShippingAddress!)
                      Container(
                        margin: EdgeInsets.symmetric(vertical: 8),
                        height: 60,
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: <Widget>[
                            SwitchAddressTab(
                                title: trans("Billing Details"),
                                currentTabIndex: activeTabIndex,
                                type: "billing",
                                onTapAction: () => setState(() {
                                      activeTabIndex = 0;
                                      activeTab = tabBillingDetails();
                                    })),
                            SwitchAddressTab(
                                title: trans("Shipping Address"),
                                currentTabIndex: activeTabIndex,
                                type: "shipping",
                                onTapAction: () => setState(() {
                                      activeTabIndex = 1;
                                      activeTab = tabShippingDetails();
                                    })),
                          ],
                        ),
                      ),
                    // Address Selection Section
                    _buildAddressSelectionCard(),

                    SizedBox(height: 16),

                    // Form content section - FIXED: Use SizedBox instead of Expanded to prevent infinite size
                    SizedBox(
                      height: MediaQuery.of(context).size.height * 0.5, // Reduced height to accommodate address section
                      child: Container(
                        decoration: BoxDecoration(
                          color: ThemeColor.get(context).backgroundContainer,
                          borderRadius: BorderRadius.circular(10),
                          boxShadow: (Theme.of(context).brightness == Brightness.light)
                              ? wsBoxShadow()
                              : null,
                        ),
                        padding: EdgeInsets.all(8),
                        margin: EdgeInsets.symmetric(horizontal: 8),
                        child: SingleChildScrollView(
                          child: (activeTab ?? tabBillingDetails()),
                        ),
                      ),
                    ),
                    // Bottom section with options and button
                    Container(
                      padding: EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Theme.of(context).scaffoldBackgroundColor,
                        border: Border(
                          top: BorderSide(
                            color: Colors.grey[300]!,
                            width: 1,
                          ),
                        ),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: <Widget>[
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: <Widget>[
                              Expanded(
                                child: Text(
                                  trans("Ship to a different address?"),
                                  style: Theme.of(context).textTheme.bodyMedium,
                                ),
                              ),
                              Checkbox(
                                value: _hasDifferentShippingAddress,
                                onChanged: _onChangeShipping,
                              )
                            ],
                          ),
                          if (_wpLoginEnabled == true)
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: <Widget>[
                                Expanded(
                                  child: Text(
                                    trans("Remember my details"),
                                    style: Theme.of(context).textTheme.bodyMedium,
                                  ),
                                ),
                                Checkbox(
                                  value: valRememberDetails,
                                  onChanged: (bool? value) {
                                    setState(() {
                                      valRememberDetails = value;
                                    });
                                  },
                                )
                              ],
                            ),
                          SizedBox(height: 16),
                          PrimaryButton(
                            title: trans("USE DETAILS"),
                            action: _useDetailsTapped,
                            isLoading: isLocked('update_shipping'),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  _useDetailsTapped() async {
    await lockRelease('update_shipping', perform: () async {
      CustomerAddress customerBillingAddress = _setCustomerAddress(
        firstName: _txtBillingFirstName.text,
        lastName: _txtBillingLastName.text,
        addressLine: _txtBillingAddressLine.text,
        city: _txtBillingCity.text,
        postalCode: _txtBillingPostalCode.text,
        phoneNumber: _txtBillingPhoneNumber.text,
        emailAddress: _txtBillingEmailAddress.text.trim(),
        customerCountry: _billingCountry,
      );

      CheckoutSession.getInstance.billingDetails!.shippingAddress =
          customerBillingAddress;
      CheckoutSession.getInstance.billingDetails!.billingAddress =
          customerBillingAddress;

      // Update delivery cost based on selected city
      _updateDeliveryCostForCity(customerBillingAddress.city);

      if (_hasDifferentShippingAddress == true) {
        CustomerAddress customerShippingAddress = _setCustomerAddress(
            firstName: _txtShippingFirstName.text,
            lastName: _txtShippingLastName.text,
            addressLine: _txtShippingAddressLine.text,
            city: _txtShippingCity.text,
            postalCode: _txtShippingPostalCode.text,
            emailAddress: _txtShippingEmailAddress.text.trim(),
            customerCountry: _shippingCountry);

        if (customerShippingAddress.hasMissingFields()) {
          showToast(
            title: trans("Oops"),
            description: trans(
                "Invalid shipping address, please check your shipping details"),
            style: ToastNotificationStyleType.warning,
          );
          return;
        }

        CheckoutSession.getInstance.billingDetails!.shippingAddress =
            customerShippingAddress;

        // Update delivery cost for shipping city if different
        _updateDeliveryCostForCity(customerShippingAddress.city);
      }

      BillingDetails billingDetails =
          CheckoutSession.getInstance.billingDetails!;

      // Email validation
      String billingEmail = billingDetails.billingAddress?.emailAddress ?? "";
      String shippingEmail = billingDetails.shippingAddress?.emailAddress ?? "";
      // Billing email is required for Stripe
      if (billingEmail.isEmpty || !validate.isEmail(billingEmail)) {
        showToast(
          title: trans("Oops"),
          description: trans("Please enter a valid billing email"),
          style: ToastNotificationStyleType.warning,
        );
        return;
      }

      if (shippingEmail.isNotEmpty && !validate.isEmail(shippingEmail)) {
        showToast(
          title: trans("Oops"),
          description: trans("Please enter a valid shipping email"),
          style: ToastNotificationStyleType.warning,
        );
        return;
      }

      // Update WP shipping info for user
      if (_wpLoginEnabled == true && (await WPJsonAPI.wpUserLoggedIn())) {
        try {
          await WPJsonAPI.instance.api(
            (request) => request.wpUpdateUserInfo(metaData: [
              ...?billingDetails.billingAddress?.toUserMetaDataItem('billing'),
              ...?billingDetails.shippingAddress
                  ?.toUserMetaDataItem('shipping'),
            ]),
          );
        } on Exception catch (e) {
          showToast(
              title: trans("Oops!"),
              description: trans("Something went wrong"),
              icon: Icons.info_outline,
              style: ToastNotificationStyleType.danger);
          if (getEnv('APP_DEBUG', defaultValue: true) == true) {
            NyLogger.error(e.toString());
          }
        }
      }

      if (valRememberDetails == true) {
        await CheckoutSession.getInstance.saveBillingAddress();
        await CheckoutSession.getInstance.saveShippingAddress();
      } else {
        await CheckoutSession.getInstance.clearBillingAddress();
        await CheckoutSession.getInstance.clearShippingAddress();
      }

      CheckoutSession.getInstance.billingDetails!.rememberDetails =
          valRememberDetails;
      CheckoutSession.getInstance.shipToDifferentAddress =
          _hasDifferentShippingAddress;

      CheckoutSession.getInstance.shippingType = null;
      pop();
    });
  }

  _onChangeShipping(bool? value) async {
    _hasDifferentShippingAddress = value;
    activeTabIndex = 1;
    activeTab = value == true ? tabShippingDetails() : tabBillingDetails();

    CustomerAddress? sfCustomerShippingAddress =
        await CheckoutSession.getInstance.getShippingAddress();
    if (sfCustomerShippingAddress == null) {
      _setFields(
          firstName: "",
          lastName: "",
          addressLine: "",
          city: "",
          postalCode: "",
          phoneNumber: "",
          emailAddress: "",
          customerCountry: CustomerCountry());
    }
    setState(() {});
  }

  CustomerAddress _setCustomerAddress(
      {required String firstName,
      required String lastName,
      required String addressLine,
      required String city,
      required String postalCode,
      required String emailAddress,
      String? phoneNumber,
      required CustomerCountry? customerCountry}) {
    CustomerAddress customerShippingAddress = CustomerAddress();
    customerShippingAddress.firstName = firstName;
    customerShippingAddress.lastName = lastName;
    customerShippingAddress.addressLine = addressLine;
    customerShippingAddress.city = city;
    customerShippingAddress.postalCode = postalCode;
    if (phoneNumber != null && phoneNumber != "") {
      customerShippingAddress.phoneNumber = phoneNumber;
    }
    customerShippingAddress.customerCountry = customerCountry;
    customerShippingAddress.emailAddress = emailAddress;
    return customerShippingAddress;
  }

  _navigateToSelectCountry({required String type}) {
    // Force Libya only - prevent country selection
    showToastNotification(
      context,
      title: "معلومات", // "Information" in Arabic
      description: "الدولة محددة مسبقاً: ليبيا", // "Country is pre-set: Libya" in Arabic
      icon: Icons.info,
    );

    // Ensure Libya is set
    final libyaCountry = CustomerCountry(
      countryCode: 'LY',
      name: 'Libya',
    );

    if (type == "billing") {
      _billingCountry = libyaCountry;
      activeTab = tabBillingDetails();
    } else if (type == "shipping") {
      _shippingCountry = libyaCountry;
      activeTab = tabShippingDetails();
    }
    setState(() {});

    // Original navigation code commented out to prevent country change
    /*
    routeTo(CustomerCountriesPage.path, onPop: (value) {
      if (value == null) {
        return;
      }
      if (type == "billing") {
        _billingCountry = CustomerCountry.fromDefaultShipping(
            defaultShipping: value as DefaultShipping);
        activeTab = tabBillingDetails();
      } else if (type == "shipping") {
        _shippingCountry = CustomerCountry.fromDefaultShipping(
            defaultShipping: value as DefaultShipping);
        activeTab = tabShippingDetails();
      }
      setState(() {});
    });
    */
  }

  _fetchUserDetails() async {
    await lockRelease('load_shipping_info', perform: () async {
      WPUserInfoResponse? wpUserInfoResponse;
      try {
        wpUserInfoResponse =
            await WPJsonAPI.instance.api((request) => request.wpGetUserInfo());
      } on Exception catch (e) {
        printError(e.toString());
        showToast(
            title: trans("Oops!"),
            description: trans("Something went wrong"),
            icon: Icons.info_outline,
            style: ToastNotificationStyleType.danger);
        pop();
        return;
      }

      if (wpUserInfoResponse != null && wpUserInfoResponse.status == 200) {
        BillingDetails billingDetails =
            await billingDetailsFromWpUserInfoResponse(wpUserInfoResponse);

        _setFieldsFromCustomerAddress(billingDetails.shippingAddress,
            type: "shipping");
        _setFieldsFromCustomerAddress(billingDetails.billingAddress,
            type: "billing");

        setState(() {});
      }
    });
  }

  /// Update delivery cost based on selected Libyan city
  /// MASTER'S ORDER: CheckoutDetailsPage is the SOLE AUTHORITY for setting CheckoutSession.shippingType
  Future<void> _updateDeliveryCostForCity(String? cityName) async {
    if (cityName == null || cityName.isEmpty) return;

    print('=== SOLE AUTHORITY: CheckoutDetailsPage Setting Shipping Cost for City: $cityName ===');

    final deliveryService = LibyanDeliveryService();
    final wooCommerceService = WooCommerceService();

    // Check if the city is a valid Libyan city
    if (deliveryService.isCityValid(cityName)) {
      try {
        // Fetch dynamic shipping cost from API
        final dynamicCost = await wooCommerceService.fetchDynamicShippingCost(cityName);
        print('🔍 Dynamic API returned cost: $dynamicCost LYD for $cityName');

        // CRITICAL: Construct complete CustomShippingMethod object
        final city = LibyanCitiesData.findCityByName(cityName) ??
                     LibyanCitiesData.findCityByArabicName(cityName);

        CustomShippingMethod shippingMethodForSession = CustomShippingMethod(
          title: "التوصيل (درب السبيل)", // Arabic: "Delivery (Darb Al-Sabil)"
          methodId: "v_shipping_by_city",
          cost: dynamicCost.toString(), // Convert double to string for CustomShippingMethod
          description: 'توصيل إلى ${city?.getDisplayName() ?? cityName}',
        );

        // CRITICAL: Construct complete ShippingType object
        ShippingType completeShippingType = ShippingType(
          methodId: "v_shipping_by_city",
          cost: dynamicCost.toString(), // Convert double to string for ShippingType
          object: shippingMethodForSession, // CRITICAL: Fully formed object
          minimumValue: null,
        );

        // MASTER'S ORDER: Directly update the central CheckoutSession (Single Source of Truth)
        CheckoutSession.getInstance.shippingType = completeShippingType;

        print('✅ SOLE AUTHORITY: CheckoutDetailsPage set complete ShippingType in CheckoutSession');
        print('   Method ID: ${completeShippingType.methodId}');
        print('   Cost: ${completeShippingType.cost}');
        print('   Object Type: ${completeShippingType.object.runtimeType}');
        print('   Object Title: ${completeShippingType.object?.title}');
        print('   Object Cost: ${completeShippingType.object?.cost}');

      } catch (e) {
        print('⚠️ Error fetching dynamic cost for $cityName: $e');
        // Fallback to static cost
        final staticCost = deliveryService.getDeliveryCostForCity(cityName);

        CustomShippingMethod fallbackShippingMethod = CustomShippingMethod(
          title: "التوصيل (درب السبيل)",
          methodId: "v_shipping_by_city",
          cost: staticCost.toString(),
          description: 'توصيل إلى $cityName',
        );

        ShippingType fallbackShippingType = ShippingType(
          methodId: "v_shipping_by_city",
          cost: staticCost.toString(),
          object: fallbackShippingMethod,
          minimumValue: null,
        );

        CheckoutSession.getInstance.shippingType = fallbackShippingType;
        print('✅ FALLBACK: Set static cost $staticCost LYD for $cityName');
      }
    } else {
      print('⚠️ City $cityName not found in Libyan cities list');
    }
  }

  Widget _buildAddressSelectionCard() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 8),
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'عنوان التوصيل',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF2E3A59),
                ),
              ),
              TextButton(
                onPressed: _navigateToAddressManagement,
                child: Text(
                  'إدارة العناوين',
                  style: TextStyle(
                    color: Color(0xFFB76E79),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 12),
          FutureBuilder<CustomerAddress?>(
            future: CheckoutSession.getInstance.getDefaultAddress(),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Container(
                  height: 60,
                  child: Center(child: CircularProgressIndicator()),
                );
              }

              if (snapshot.hasData && snapshot.data != null) {
                final address = snapshot.data!;
                return Container(
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Color(0xFFF8F9FA),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Color(0xFFE0E0E0)),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.location_on,
                        color: Color(0xFFB76E79),
                        size: 20,
                      ),
                      SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${address.firstName} ${address.lastName}',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: Color(0xFF2E3A59),
                              ),
                            ),
                            SizedBox(height: 2),
                            Text(
                              address.addressFull(),
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              }

              return Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Color(0xFFF8F9FA),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Color(0xFFE0E0E0)),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.add_location,
                      color: Colors.grey[400],
                      size: 20,
                    ),
                    SizedBox(width: 12),
                    Text(
                      'لم يتم تحديد عنوان',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  _navigateToAddressManagement() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => _AddressManagementPageFromCheckout(),
      ),
    ).then((_) => setState(() {})); // Refresh the address display
  }
}

// Simplified Address Management for Checkout
class _AddressManagementPageFromCheckout extends StatefulWidget {
  @override
  _AddressManagementPageFromCheckoutState createState() => _AddressManagementPageFromCheckoutState();
}

class _AddressManagementPageFromCheckoutState extends State<_AddressManagementPageFromCheckout> {
  List<CustomerAddress> _addresses = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAddresses();
  }

  _loadAddresses() async {
    setState(() => _isLoading = true);
    _addresses = await CheckoutSession.getInstance.getAddressBook();
    setState(() => _isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFFEF8F8),
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          'اختيار عنوان التوصيل',
          style: TextStyle(
            color: Color(0xFF2E3A59),
            fontWeight: FontWeight.bold,
            fontSize: 18,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios, color: Color(0xFF2E3A59)),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : _addresses.isEmpty
              ? _buildEmptyState()
              : _buildAddressList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.location_off,
            size: 80,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16),
          Text(
            'لا توجد عناوين محفوظة',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF2E3A59),
            ),
          ),
          SizedBox(height: 8),
          Text(
            'يرجى إضافة عنوان من صفحة الإعدادات',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddressList() {
    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: _addresses.length,
      itemBuilder: (context, index) {
        final address = _addresses[index];
        return _buildAddressCard(address);
      },
    );
  }

  Widget _buildAddressCard(CustomerAddress address) {
    bool isSelected = address.isDefault ?? false;

    return Container(
      margin: EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected ? Color(0xFFFF8C00) : Colors.grey[300]!,
          width: isSelected ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _selectAddress(address.addressId!),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Row(
            children: [
              // Selection indicator
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isSelected ? Color(0xFFFF8C00) : Colors.transparent,
                  border: Border.all(
                    color: isSelected ? Color(0xFFFF8C00) : Colors.grey[400]!,
                    width: 2,
                  ),
                ),
                child: isSelected
                    ? Icon(Icons.check, color: Colors.white, size: 16)
                    : null,
              ),
              SizedBox(width: 16),

              // Address details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      address.addressLabel ?? 'عنوان',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF2E3A59),
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      '${address.firstName} ${address.lastName}',
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFF2E3A59),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: 2),
                    Text(
                      address.addressFull(),
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.grey[600],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _selectAddress(String addressId) async {
    await CheckoutSession.getInstance.setSelectedAddress(addressId);
    Navigator.pop(context); // Return to checkout
  }
}
