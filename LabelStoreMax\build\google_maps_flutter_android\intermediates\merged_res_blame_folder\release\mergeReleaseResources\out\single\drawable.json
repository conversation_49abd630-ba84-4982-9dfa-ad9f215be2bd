[{"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/drawable/common_google_signin_btn_text_dark.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-jetified-play-services-base-18.0.1-18:/drawable/common_google_signin_btn_text_dark.xml"}, {"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/drawable/common_google_signin_btn_text_light_focused.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-jetified-play-services-base-18.0.1-18:/drawable/common_google_signin_btn_text_light_focused.xml"}, {"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/drawable/notification_tile_bg.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-core-1.13.1-2:/drawable/notification_tile_bg.xml"}, {"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/drawable/common_google_signin_btn_icon_dark_focused.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-jetified-play-services-base-18.0.1-18:/drawable/common_google_signin_btn_icon_dark_focused.xml"}, {"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/drawable/common_google_signin_btn_icon_light.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-jetified-play-services-base-18.0.1-18:/drawable/common_google_signin_btn_icon_light.xml"}, {"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/drawable/amu_bubble_shadow.9.png", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-jetified-android-maps-utils-3.6.0-5:/drawable/amu_bubble_shadow.9.png"}, {"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/drawable/common_google_signin_btn_icon_light_normal.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-jetified-play-services-base-18.0.1-18:/drawable/common_google_signin_btn_icon_light_normal.xml"}, {"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/drawable/common_google_signin_btn_text_disabled.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-jetified-play-services-base-18.0.1-18:/drawable/common_google_signin_btn_text_disabled.xml"}, {"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/drawable/common_google_signin_btn_text_light.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-jetified-play-services-base-18.0.1-18:/drawable/common_google_signin_btn_text_light.xml"}, {"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/drawable/common_google_signin_btn_icon_disabled.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-jetified-play-services-base-18.0.1-18:/drawable/common_google_signin_btn_icon_disabled.xml"}, {"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/drawable/common_google_signin_btn_icon_light_focused.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-jetified-play-services-base-18.0.1-18:/drawable/common_google_signin_btn_icon_light_focused.xml"}, {"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/drawable/common_google_signin_btn_text_light_normal.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-jetified-play-services-base-18.0.1-18:/drawable/common_google_signin_btn_text_light_normal.xml"}, {"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/drawable/common_google_signin_btn_text_dark_focused.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-jetified-play-services-base-18.0.1-18:/drawable/common_google_signin_btn_text_dark_focused.xml"}, {"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/drawable/notification_bg.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-core-1.13.1-2:/drawable/notification_bg.xml"}, {"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/drawable/common_google_signin_btn_text_dark_normal.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-jetified-play-services-base-18.0.1-18:/drawable/common_google_signin_btn_text_dark_normal.xml"}, {"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/drawable/notification_icon_background.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-core-1.13.1-2:/drawable/notification_icon_background.xml"}, {"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/drawable/amu_bubble_mask.9.png", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-jetified-android-maps-utils-3.6.0-5:/drawable/amu_bubble_mask.9.png"}, {"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/drawable/common_google_signin_btn_icon_dark.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-jetified-play-services-base-18.0.1-18:/drawable/common_google_signin_btn_icon_dark.xml"}, {"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/drawable/common_google_signin_btn_icon_dark_normal.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-jetified-play-services-base-18.0.1-18:/drawable/common_google_signin_btn_icon_dark_normal.xml"}, {"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/drawable/notification_bg_low.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-core-1.13.1-2:/drawable/notification_bg_low.xml"}]