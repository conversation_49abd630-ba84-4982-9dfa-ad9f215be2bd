//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'dart:convert';
import '/app/models/billing_details.dart';
import '/app/models/cart.dart';
import '/app/models/customer_address.dart';
import '/app/models/payment_type.dart';
import '/app/models/shipping_type.dart';
import '/bootstrap/shared_pref/shared_key.dart';
import 'package:nylo_framework/nylo_framework.dart';
import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';


class CheckoutSession {
  bool? shipToDifferentAddress = false;

  CheckoutSession._privateConstructor();
  static final CheckoutSession getInstance =
      CheckoutSession._privateConstructor();

  BillingDetails? billingDetails;
  ShippingType? shippingType;
  PaymentType? paymentType;
  WooCoupon? coupon;
  String? customerNote;

  void initSession() {
    billingDetails = BillingDetails();
    shippingType = null;
  }

  void clear() {
    billingDetails = null;
    shippingType = null;
    paymentType = null;
    coupon = null;
  }

  saveBillingAddress() async {
    CustomerAddress? customerAddress =
        CheckoutSession.getInstance.billingDetails!.billingAddress;

    if (customerAddress == null) {
      return;
    }

    String billingAddress = jsonEncode(customerAddress.toJson());
    await NyStorage.save(SharedKey.customerBillingDetails, billingAddress);
  }

  Future<CustomerAddress?> getBillingAddress() async {
    String? strCheckoutDetails =
        await (NyStorage.read(SharedKey.customerBillingDetails));

    if (strCheckoutDetails != null && strCheckoutDetails != "") {
      return CustomerAddress.fromJson(jsonDecode(strCheckoutDetails));
    }
    return null;
  }

  clearBillingAddress() async =>
      await NyStorage.delete(SharedKey.customerBillingDetails);

  saveShippingAddress() async {
    CustomerAddress? customerAddress =
        CheckoutSession.getInstance.billingDetails!.shippingAddress;
    if (customerAddress == null) {
      return;
    }
    String shippingAddress = jsonEncode(customerAddress.toJson());
    await NyStorage.save(SharedKey.customerShippingDetails, shippingAddress);
  }

  Future<CustomerAddress?> getShippingAddress() async {
    String? strCheckoutDetails =
        await (NyStorage.read(SharedKey.customerShippingDetails));
    if (strCheckoutDetails != null && strCheckoutDetails != "") {
      return CustomerAddress.fromJson(jsonDecode(strCheckoutDetails));
    }
    return null;
  }

  clearShippingAddress() async =>
      await NyStorage.delete(SharedKey.customerShippingDetails);

  // Address Book Management
  saveAddressBook(List<CustomerAddress> addresses) async {
    List<Map<String, dynamic>> addressList = addresses.map((addr) => addr.toJson()).toList();
    String addressBookJson = jsonEncode(addressList);
    await NyStorage.save('customer_address_book', addressBookJson);
  }

  Future<List<CustomerAddress>> getAddressBook() async {
    String? addressBookJson = await NyStorage.read('customer_address_book');
    if (addressBookJson != null && addressBookJson.isNotEmpty) {
      List<dynamic> addressList = jsonDecode(addressBookJson);
      return addressList.map((addr) => CustomerAddress.fromJson(addr)).toList();
    }
    return [];
  }

  addNewAddress(CustomerAddress address) async {
    List<CustomerAddress> addresses = await getAddressBook();

    // Generate unique ID if not provided
    if (address.addressId == null || address.addressId!.isEmpty) {
      address.addressId = DateTime.now().millisecondsSinceEpoch.toString();
    }

    // If this is the first address or marked as default, make it default
    if (addresses.isEmpty || address.isDefault == true) {
      // Clear other default addresses
      for (var addr in addresses) {
        addr.isDefault = false;
      }
      address.isDefault = true;
    }

    addresses.add(address);
    await saveAddressBook(addresses);
  }

  deleteAddress(String addressId) async {
    List<CustomerAddress> addresses = await getAddressBook();
    addresses.removeWhere((addr) => addr.addressId == addressId);

    // If we deleted the default address, make the first remaining address default
    if (addresses.isNotEmpty && !addresses.any((addr) => addr.isDefault == true)) {
      addresses.first.isDefault = true;
    }

    await saveAddressBook(addresses);
  }

  setSelectedAddress(String addressId) async {
    List<CustomerAddress> addresses = await getAddressBook();

    // Clear all default flags
    for (var addr in addresses) {
      addr.isDefault = false;
    }

    // Set the selected address as default
    CustomerAddress? selectedAddress = addresses.firstWhere(
      (addr) => addr.addressId == addressId,
      orElse: () => CustomerAddress(),
    );

    if (selectedAddress.addressId != null) {
      selectedAddress.isDefault = true;
      await saveAddressBook(addresses);

      // Update current checkout session
      billingDetails?.billingAddress = selectedAddress;
      billingDetails?.shippingAddress = selectedAddress;
    }
  }

  Future<CustomerAddress?> getDefaultAddress() async {
    List<CustomerAddress> addresses = await getAddressBook();
    try {
      return addresses.firstWhere((addr) => addr.isDefault == true);
    } catch (e) {
      return addresses.isNotEmpty ? addresses.first : null;
    }
  }

  Future<String> total({bool withFormat = false}) async {
    double totalCart = double.tryParse(await Cart.getInstance.getTotal()) ?? 0.0;
    double totalShipping = 0;
    if (shippingType != null) {
      switch (shippingType!.methodId) {
        case "flat_rate":
          totalShipping = double.tryParse(shippingType!.cost) ?? 0.0;
          break;
        case "free_shipping":
          totalShipping = double.tryParse(shippingType!.cost) ?? 0.0;
          break;
        case "local_pickup":
          totalShipping = double.tryParse(shippingType!.cost) ?? 0.0;
          break;
        case "v_shipping_by_city":
          totalShipping = double.tryParse(shippingType!.cost) ?? 0.0;
          break;
        default:
          // For any other shipping method, use the cost
          totalShipping = double.tryParse(shippingType!.cost) ?? 0.0;
          break;
      }
    }

    double total = totalCart + totalShipping;

    if (withFormat == true) {
      return total.toStringAsFixed(2); // Simple formatting for now
    }
    return total.toStringAsFixed(2);
  }
}
