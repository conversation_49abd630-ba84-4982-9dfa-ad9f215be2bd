//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import '/app/models/cart.dart';
import '/app/models/checkout_session.dart';
import 'package:nylo_framework/nylo_framework.dart';
import 'package:lottie/lottie.dart';
import '/resources/widgets/account_detail_orders_widget.dart';

class CheckoutStatusPage extends NyStatefulWidget {
  static RouteView path = ("/checkout-status", (_) => CheckoutStatusPage());

  CheckoutStatusPage({super.key}) : super(child: () => _CheckoutStatusState());
}

class _CheckoutStatusState extends NyState<CheckoutStatusPage> {

  @override
  get init => () async {
        await Cart.getInstance.clear();
        CheckoutSession.getInstance.clear();
      };

  @override
  Widget view(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFF4DDE2), // Light pink background from brand palette
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        automaticallyImplyLeading: false,
        centerTitle: true,
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Main content area
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.symmetric(horizontal: 24, vertical: 20),
                child: Column(
                  children: [
                    // Heart decorations at top
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Icon(
                          Icons.favorite,
                          color: Color(0xFFB76E79).withValues(alpha: 0.6),
                          size: 40,
                        ),
                        Icon(
                          Icons.favorite,
                          color: Color(0xFFB76E79).withValues(alpha: 0.4),
                          size: 60,
                        ),
                        Icon(
                          Icons.favorite,
                          color: Color(0xFFB76E79).withValues(alpha: 0.6),
                          size: 40,
                        ),
                      ],
                    ),

                    SizedBox(height: 40),

                    // Main thank you text
                    Text(
                      'شكرا على طلبك!',
                      style: TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2E3A59),
                      ),
                      textAlign: TextAlign.center,
                    ),

                    SizedBox(height: 16),

                    // Subtitle text
                    Text(
                      'شكرا على اختيارك Velvete\nيمكنك متابعة تفاصيل طلبك\nمن الملف الشخصي "تاريخ الطلبات"',
                      style: TextStyle(
                        fontSize: 16,
                        color: Color(0xFF2E3A59),
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),

                    SizedBox(height: 40),

                    // Order Summary Button
                    Container(
                      width: double.infinity,
                      height: 56,
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => Scaffold(
                                appBar: AppBar(
                                  title: Text('تاريخ الطلبات'),
                                  leading: IconButton(
                                    icon: Icon(Icons.arrow_back_ios),
                                    onPressed: () => Navigator.pop(context),
                                  ),
                                ),
                                body: SafeArea(
                                  child: AccountDetailOrdersWidget(),
                                ),
                              ),
                            ),
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Color(0xFFB76E79),
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(28),
                          ),
                          elevation: 2,
                        ),
                        child: Text(
                          'ملخص طلبك',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: 40),

                    // Animated woman with shopping bag
                    Container(
                      height: 300,
                      child: Lottie.network(
                        'https://velvete.ly/wp-content/uploads/2025/07/Girl-holding-bag.json',
                        fit: BoxFit.contain,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            height: 300,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.shopping_bag,
                                  size: 120,
                                  color: Color(0xFFB76E79),
                                ),
                                SizedBox(height: 16),
                                Text(
                                  'شكرا لك!',
                                  style: TextStyle(
                                    fontSize: 24,
                                    color: Color(0xFF2E3A59),
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),

                    SizedBox(height: 40),

                    // Additional heart decorations
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.favorite,
                          color: Color(0xFFB76E79).withValues(alpha: 0.5),
                          size: 30,
                        ),
                        SizedBox(width: 20),
                        Icon(
                          Icons.favorite,
                          color: Color(0xFFB76E79).withValues(alpha: 0.7),
                          size: 40,
                        ),
                        SizedBox(width: 20),
                        Icon(
                          Icons.favorite,
                          color: Color(0xFFB76E79).withValues(alpha: 0.5),
                          size: 30,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            // Bottom button
            Container(
              padding: EdgeInsets.all(24),
              child: Container(
                width: double.infinity,
                height: 56,
                child: ElevatedButton(
                  onPressed: () {
                    routeToInitial();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(0xFFB76E79),
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(28),
                    ),
                    elevation: 2,
                  ),
                  child: Text(
                    'العودة إلى الصفحة الرئيسية',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
