{"logs": [{"outputFile": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/values-en-rIN/values-en-rIN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36906ab11f430537c15ad017404b649b\\transformed\\core-1.13.1\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}}]}, {"outputFile": "io.flutter.plugins.googlemaps.google_maps_flutter_android-mergeReleaseResources-28:/values-en-rIN/values-en-rIN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\36906ab11f430537c15ad017404b649b\\transformed\\core-1.13.1\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}}]}]}