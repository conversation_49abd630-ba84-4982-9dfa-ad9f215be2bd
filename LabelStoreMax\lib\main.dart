import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '/bootstrap/app.dart';
import '/bootstrap/boot.dart';
import 'package:nylo_framework/nylo_framework.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Suppress RenderFlex overflow errors
  FlutterError.onError = (FlutterErrorDetails details) {
    // Check if this is a RenderFlex overflow error
    if (details.exception.toString().contains('RenderFlex overflowed')) {
      // Suppress these specific errors
      return;
    }
    // For all other errors, use the default handler
    FlutterError.presentError(details);
  };

  // Initialize Facebook SDK very early to prevent GeneratedPluginRegistrant errors
  try {
    // For mobile platforms, the SDK is auto-initialized by the native plugin
    // For web/desktop, we need explicit initialization
    if (kIsWeb) {
      await FacebookAuth.instance.webAndDesktopInitialize(
        appId: "1234567890123456", // Placeholder - configure in Facebook Developer Console
        cookie: true,
        xfbml: true,
        version: "v18.0",
      );
    }
    print('✅ Facebook SDK initialized successfully');
  } catch (e) {
    print('⚠️ Facebook SDK initialization failed: $e');
    // Continue app initialization even if Facebook SDK fails
  }

  try {
    print('Starting Nylo initialization...');
    Nylo nylo = await Nylo.init(setup: Boot.nylo, setupFinished: Boot.finished);
    print('Nylo initialization completed successfully');

    print('Starting app...');
    runApp(
      AppBuild(
        navigatorKey: NyNavigator.instance.router.navigatorKey,
        onGenerateRoute: nylo.router!.generator(),
        initialRoute: nylo.getInitialRoute(),
        navigatorObservers: [
          ...nylo.getNavigatorObservers(),
        ],
        debugShowCheckedModeBanner: false,
      ),
    );
    print('App started successfully');
  } catch (e, stackTrace) {
    print('CRITICAL ERROR in main(): $e');
    print('Stack trace: $stackTrace');

    // Fallback to a simple app to prevent complete crash
    runApp(
      MaterialApp(
        home: Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error, size: 64, color: Colors.red),
                SizedBox(height: 16),
                Text('App Initialization Failed'),
                SizedBox(height: 8),
                Text('Error: $e', style: TextStyle(fontSize: 12)),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
