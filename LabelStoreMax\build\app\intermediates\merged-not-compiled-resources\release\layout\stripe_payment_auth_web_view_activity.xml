<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:windowSoftInputMode="adjustResize"
    android:fitsSystemWindows="true">

    <FrameLayout
        android:id="@+id/web_view_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <com.stripe.android.view.PaymentAuthWebView
            android:id="@+id/web_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:windowSoftInputMode="adjustResize"
            android:focusable="true"
            android:focusableInTouchMode="true" />
    </FrameLayout>

    <com.google.android.material.appbar.AppBarLayout
        android:fitsSystemWindows="true"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:elevation="@dimen/stripe_toolbar_elevation">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            android:theme="@style/StripeToolBarStyle"
            app:title="@string/stripe_secure_checkout" />
    </com.google.android.material.appbar.AppBarLayout>

    <com.google.android.material.progressindicator.CircularProgressIndicator
        android:id="@+id/progress_bar"
        style="@style/Widget.MaterialComponents.CircularProgressIndicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:indeterminate="true" />
</androidx.coordinatorlayout.widget.CoordinatorLayout>
