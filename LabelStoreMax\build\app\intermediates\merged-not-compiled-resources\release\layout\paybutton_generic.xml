<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        tools:ignore="Overdraw"
        android:id="@+id/pay_button_view"
        android:layout_height="wrap_content"
        android:layout_width="match_parent"
        android:minHeight="@dimen/pay_button_generic_min_height"
        android:minWidth="@dimen/pay_button_generic_min_width"
        android:background="?attr/payButtonGenericBackground"
        android:gravity="center"
        android:orientation="horizontal">

       <ImageView
            android:id="@+id/pay_button_logo"
            android:src="?attr/payButtonGenericLogoImage"
            android:layout_width="@dimen/pay_image_generic_width"
            android:layout_height="@dimen/pay_image_generic_height"
            android:importantForAccessibility="no"/>
</LinearLayout>
