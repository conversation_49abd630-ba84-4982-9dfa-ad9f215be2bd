import 'package:flutter/material.dart';
import '/resources/themes/styles/color_styles.dart';

/* Light Theme Colors - Velvete Brand Colors
|-------------------------------------------------------------------------- */

class LightThemeColors implements ColorStyles {
  // === LUXURIOUS LIGHT THEME PALETTE ===

  // Primary Background & Surfaces
  @override
  Color get background => const Color(0xFFFFFFFF); // Pure White: #FFFFFF
  @override
  Color get backgroundContainer => const Color(0xFFFFF9F9); // Soft Blush White: #FFF9F9
  @override
  Color get surfaceBackground => const Color(0xFFFFF9F9); // Soft Blush White for cards: #FFF9F9

  // Text Colors - Sophisticated Hierarchy
  @override
  Color get content => const Color(0xFF2E2E2E); // Rich Charcoal: #2E2E2E
  @override
  Color get surfaceContent => const Color(0xFF2E2E2E); // Rich Charcoal for surface text: #2E2E2E
  @override
  Color get inputPrimaryContent => const Color(0xFF2E2E2E); // Rich Charcoal for input text: #2E2E2E

  // Brand Accent Colors - Elegant Mauve Palette
  @override
  Color get primaryAccent => const Color(0xFFA45C7B); // Deeper Mauve: #A45C7B (primary accent)

  // App Bar - Clean & Sophisticated
  @override
  Color get appBarBackground => const Color(0xFFFFFFFF); // Pure White app bar: #FFFFFF
  @override
  Color get appBarPrimaryContent => const Color(0xFF2E2E2E); // Rich Charcoal for app bar text: #2E2E2E

  // Buttons - Impactful & Clear
  @override
  Color get buttonBackground => const Color(0xFF9A0000); // Oxblood Red for primary CTAs: #9A0000
  @override
  Color get buttonContent => const Color(0xFFFFFFFF); // Pure White text on primary buttons
  @override
  Color get buttonSecondaryBackground => Colors.transparent; // Transparent for outlined buttons
  @override
  Color get buttonSecondaryContent => const Color(0xFFA45C7B); // Deeper Mauve for secondary button text: #A45C7B

  // Bottom Navigation - Elegant & Clear
  @override
  Color get bottomTabBarBackground => const Color(0xFFFFFFFF); // Pure White background for bottom nav

  // bottom tab bar - icons
  @override
  Color get bottomTabBarIconSelected => const Color(0xFFA45C7B); // Deeper Mauve for selected icon: #A45C7B
  @override
  Color get bottomTabBarIconUnselected => const Color(0xFF2E2E2E); // Rich Charcoal for unselected icons: #2E2E2E

  // bottom tab bar - label
  @override
  Color get bottomTabBarLabelUnselected => const Color(0xFF2E2E2E); // Rich Charcoal for unselected labels: #2E2E2E
  @override
  Color get bottomTabBarLabelSelected => const Color(0xFFA45C7B); // Deeper Mauve for selected label: #A45C7B

  // === STANDARDIZED BRAND COLORS IMPLEMENTATION ===

  // Primary Brand Colors
  @override
  Color get brandPrimary => const Color(0xFFB76E79); // Main brand color #B76E79
  @override
  Color get brandSecondary => const Color(0xFFF4C2C2); // Secondary brand color #F4C2C2
  @override
  Color get brandAccent => const Color(0xFFA45C7B); // Accent color for highlights

  // UI State Colors
  @override
  Color get successColor => const Color(0xFF4CAF50); // Success states
  @override
  Color get warningColor => const Color(0xFFFAD440); // Warning states (golden)
  @override
  Color get errorColor => const Color(0xFF9A0000); // Error states (oxblood red)
  @override
  Color get infoColor => const Color(0xFFA45C7B); // Information states (deeper mauve)

  // Text Colors
  @override
  Color get textPrimary => const Color(0xFF2E2E2E); // Primary text color
  @override
  Color get textSecondary => const Color(0xFF666666); // Secondary text color
  @override
  Color get textMuted => const Color(0xFF999999); // Muted text color
  @override
  Color get textOnPrimary => const Color(0xFFFFFFFF); // Text on primary background

  // Surface Colors
  @override
  Color get surfacePrimary => const Color(0xFFFFFFFF); // Primary surface
  @override
  Color get surfaceSecondary => const Color(0xFFFFF9F9); // Secondary surface
  @override
  Color get surfaceElevated => const Color(0xFFFFFFFF); // Elevated surface (cards)
  @override
  Color get surfaceOverlay => const Color(0x80000000); // Overlay surface

  // Border Colors
  @override
  Color get borderPrimary => const Color(0xFFD9D9D9); // Primary border color
  @override
  Color get borderSecondary => const Color(0xFFE5E5E5); // Secondary border color
  @override
  Color get borderFocus => const Color(0xFFA45C7B); // Focus border color

  // Shadow Colors
  @override
  Color get shadowLight => const Color(0x10000000); // Light shadow
  @override
  Color get shadowMedium => const Color(0x20000000); // Medium shadow
  @override
  Color get shadowDark => const Color(0x30000000); // Dark shadow

  // Additional Accent Colors for UI Elements
  Color get coolLightGray => const Color(0xFFD9D9D9); // Cool Light Gray for borders: #D9D9D9
  Color get softBlushAccent => const Color(0xFFF6DDE2); // Soft Blush for accents: #F6DDE2
  Color get warmBlushAccent => const Color(0xFFF8CED8); // Warm Blush for highlights: #F8CED8
  Color get rosyBlushAccent => const Color(0xFFFFC4CE); // Rosy Blush for special elements: #FFC4CE
  Color get dustyRoseAccent => const Color(0xFFE78FA2); // Dusty Rose for emphasis: #E78FA2
  Color get goldenAccent => const Color(0xFFFAD440); // Golden Yellow for banners: #FAD440
}
