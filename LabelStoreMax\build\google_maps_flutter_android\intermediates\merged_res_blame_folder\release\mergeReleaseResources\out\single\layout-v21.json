[{"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/layout-v21/notification_template_custom_big.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-core-1.13.1-2:/layout-v21/notification_template_custom_big.xml"}, {"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/layout-v21/notification_action_tombstone.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-core-1.13.1-2:/layout-v21/notification_action_tombstone.xml"}, {"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/layout-v21/notification_template_icon_group.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-core-1.13.1-2:/layout-v21/notification_template_icon_group.xml"}, {"merged": "io.flutter.plugins.googlemaps.google_maps_flutter_android-release-30:/layout-v21/notification_action.xml", "source": "io.flutter.plugins.googlemaps.google_maps_flutter_android-core-1.13.1-2:/layout-v21/notification_action.xml"}]