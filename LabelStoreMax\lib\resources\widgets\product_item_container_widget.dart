import 'package:flutter/material.dart';
import '/bootstrap/helpers.dart';
import '/resources/widgets/cached_image_widget.dart';
import '/resources/themes/styles/design_constants.dart';
import '/app/services/accessibility_service.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/app/models/woocommerce_wrappers/my_product.dart';

class ProductItemContainer extends StatelessWidget {
  const ProductItemContainer({
    super.key,
    this.product,
    this.onTap,
  });

  final MyProduct? product;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    if (product == null) {
      return SizedBox.shrink();
    }

    return _AnimatedProductCard(
      onTap: onTap,
      product: product,
      child: Container(
        margin: EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(DesignConstants.cardRadius),
        ),
        child: ClipRect(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                flex: 3,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(DesignConstants.imageRadius),
                  child: Stack(
                    children: [
                      Container(
                        color: Colors.transparent,
                        height: double.infinity,
                        width: double.infinity,
                      ),
                      Hero(
                        tag: 'product_image_${product?.id}',
                        child: CachedImageWidget(
                          image: (product?.hasImages() ?? false
                              ? product?.getFirstImage()?.getSafeImageSrc() ?? getEnv("PRODUCT_PLACEHOLDER_IMAGE")
                              : getEnv("PRODUCT_PLACEHOLDER_IMAGE")),
                          fit: BoxFit.contain,
                          height: double.infinity,
                          width: double.infinity,
                          placeholder: Center(
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              backgroundColor: Colors.black12,
                              color: Colors.black54,
                            ),
                          ),
                        ),
                      ),
                    if (isProductNew(product))
                      Positioned(
                        top: 8,
                        left: 8,
                        child: Container(
                          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            "جديد!",
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    if ((product?.isOnSale() ?? false) &&
                        product?.type != "variable")
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: Container(
                          padding: EdgeInsets.all(3),
                          decoration: BoxDecoration(
                            color: Colors.white70,
                          ),
                          child: RichText(
                            textAlign: TextAlign.center,
                            text: TextSpan(
                              text: '',
                              style: Theme.of(context).textTheme.bodyLarge,
                              children: <TextSpan>[
                                TextSpan(
                                  text:
                                      "${workoutSaleDiscount(salePrice: product?.getSafeSalePrice(), priceBefore: product?.getSafeRegularPrice())}% ${trans("off")}",
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyLarge!
                                      .copyWith(
                                        color: Colors.black,
                                        fontSize: 13,
                                      ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            // Overflow-safe text section with ClipRect
            Expanded(
              child: ClipRect(
                child: Padding(
                  padding: const EdgeInsets.all(2.0), // Reduced padding
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      // Product name - overflow safe
                      Expanded(
                        flex: 2,
                        child: ClipRect(
                          child: Text(
                            product?.name ?? "",
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium!
                                .copyWith(
                                  fontSize: 10, // Reduced font size
                                  height: 1.2, // Controlled line height
                                ),
                            maxLines: 2,
                            overflow: TextOverflow.clip, // Use clip instead of ellipsis
                          ),
                        ),
                      ),
                      // Price section - overflow safe
                      Expanded(
                        flex: 1,
                        child: ClipRect(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.end,
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              // Always show original price first if on sale
                              if (product?.onSale == true && product?.regularPrice != null)
                                Text(
                                  "${formatStringCurrency(total: product?.getSafeRegularPrice())} ",
                                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                                    decoration: TextDecoration.lineThrough,
                                    color: Colors.grey[600],
                                    fontSize: 7, // Reduced font size
                                    height: 1.0, // Controlled line height
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.clip,
                                ),
                              // Current/Sale Price
                              Text(
                                "${formatStringCurrency(total: product?.onSale == true ? product?.getSafeSalePrice() : product?.getSafePrice())} ",
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyLarge!
                                    .copyWith(
                                      fontWeight: FontWeight.w800,
                                      color: product?.onSale == true ? Colors.red : Theme.of(context).colorScheme.primary,
                                      fontSize: 9, // Reduced font size
                                      height: 1.0, // Controlled line height
                                    ),
                                maxLines: 1,
                                overflow: TextOverflow.clip,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
          ),
        ),
      ),
    );
  }
}

/// Enhanced Product Card with sophisticated micro-interactions and Hero animations
class _AnimatedProductCard extends StatefulWidget {
  final VoidCallback? onTap;
  final MyProduct? product;
  final Widget child;

  const _AnimatedProductCard({
    required this.onTap,
    required this.product,
    required this.child,
  });

  @override
  State<_AnimatedProductCard> createState() => _AnimatedProductCardState();
}

class _AnimatedProductCardState extends State<_AnimatedProductCard>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _shadowController;

  late Animation<double> _scaleAnimation;
  late Animation<double> _shadowAnimation;

  @override
  void initState() {
    super.initState();

    // Scale animation for hover/press feedback
    _scaleController = AnimationController(
      duration: DesignConstants.normalAnimation,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.03,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: DesignConstants.elegantCurve,
    ));

    // Shadow animation for depth feedback
    _shadowController = AnimationController(
      duration: DesignConstants.normalAnimation,
      vsync: this,
    );
    _shadowAnimation = Tween<double>(
      begin: 1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _shadowController,
      curve: DesignConstants.smoothCurve,
    ));
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _shadowController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    _scaleController.forward();
    _shadowController.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    _resetAnimations();
  }

  void _handleTapCancel() {
    _resetAnimations();
  }

  void _resetAnimations() {
    _scaleController.reverse();
    _shadowController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    // Create semantic label for accessibility
    String semanticLabel = 'منتج ${widget.product?.name ?? "غير محدد"}';
    if (widget.product?.onSale == true) {
      semanticLabel += '، في التخفيضات';
    }
    semanticLabel += '، السعر ${formatStringCurrency(total: widget.product?.onSale == true ? widget.product?.getSafeSalePrice() : widget.product?.getSafePrice())}';

    return Semantics(
      label: semanticLabel,
      hint: 'اضغط لعرض تفاصيل المنتج',
      button: true,
      child: GestureDetector(
        onTapDown: _handleTapDown,
        onTapUp: _handleTapUp,
        onTapCancel: _handleTapCancel,
        onTap: widget.onTap,
        child: AnimatedBuilder(
          animation: Listenable.merge([
            _scaleAnimation,
            _shadowAnimation,
          ]),
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(DesignConstants.cardRadius),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                  width: 1.0,
                ),
                boxShadow: DesignConstants.cardShadow.map((shadow) {
                  return shadow.copyWith(
                    blurRadius: shadow.blurRadius * _shadowAnimation.value,
                    offset: shadow.offset * _shadowAnimation.value,
                  );
                }).toList(),
              ),
              child: widget.child,
            ),
          );
        },
      ),
    ),
    );
  }
}
