//  Label StoreMax
//
//  Created by <PERSON>.
//  2025, WooSignal Ltd. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:flutter/material.dart';
import 'package:nylo_framework/nylo_framework.dart';
import '/resources/pages/home_page.dart';

/// Enhanced Navigation Service for consistent navigation behavior
class NavigationService {
  static final NavigationService _instance = NavigationService._internal();
  factory NavigationService() => _instance;
  NavigationService._internal();

  static NavigationService get instance => _instance;

  // Navigation history stack for better back navigation
  final List<String> _navigationHistory = [];
  
  /// Safe back navigation with fallback
  static void safeBack(BuildContext context, {String? fallbackRoute}) {
    try {
      if (Navigator.canPop(context)) {
        Navigator.pop(context);
      } else {
        // Fallback to provided route or home
        final route = fallbackRoute ?? HomePage.path;
        routeTo(route, navigationType: NavigationType.pushReplace);
      }
    } catch (e) {
      debugPrint('Navigation error: $e');
      // Emergency fallback to home
      routeTo(HomePage.path, navigationType: NavigationType.pushReplace);
    }
  }

  /// Enhanced route navigation with history tracking
  static void navigateTo(String route, {
    Object? data,
    NavigationType navigationType = NavigationType.push,
    bool trackHistory = true,
  }) {
    try {
      if (trackHistory) {
        _instance._navigationHistory.add(route);
        // Keep history manageable
        if (_instance._navigationHistory.length > 10) {
          _instance._navigationHistory.removeAt(0);
        }
      }
      
      routeTo(route, data: data, navigationType: navigationType);
    } catch (e) {
      debugPrint('Navigation error: $e');
      // Fallback navigation
      routeTo(HomePage.path, navigationType: NavigationType.pushReplace);
    }
  }

  /// Get navigation history
  static List<String> getNavigationHistory() {
    return List.from(_instance._navigationHistory);
  }

  /// Clear navigation history
  static void clearHistory() {
    _instance._navigationHistory.clear();
  }

  /// Check if route exists in history
  static bool hasRouteInHistory(String route) {
    return _instance._navigationHistory.contains(route);
  }

  /// Navigate back to specific route in history
  static void backToRoute(BuildContext context, String route) {
    try {
      final index = _instance._navigationHistory.lastIndexOf(route);
      if (index != -1) {
        // Pop until we reach the desired route
        int popCount = _instance._navigationHistory.length - index - 1;
        for (int i = 0; i < popCount; i++) {
          if (Navigator.canPop(context)) {
            Navigator.pop(context);
          }
        }
        // Update history
        _instance._navigationHistory.removeRange(index + 1, _instance._navigationHistory.length);
      } else {
        // Route not in history, navigate directly
        navigateTo(route, navigationType: NavigationType.pushReplace);
      }
    } catch (e) {
      debugPrint('Back to route error: $e');
      navigateTo(route, navigationType: NavigationType.pushReplace);
    }
  }

  /// Show loading dialog during navigation
  static void showNavigationLoading(BuildContext context, {String? message}) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text(message ?? 'جاري التحميل...'),
          ],
        ),
      ),
    );
  }

  /// Hide loading dialog
  static void hideNavigationLoading(BuildContext context) {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    }
  }

  /// Navigate with loading indicator
  static Future<void> navigateWithLoading(
    BuildContext context,
    String route, {
    Object? data,
    String? loadingMessage,
    Duration delay = const Duration(milliseconds: 500),
  }) async {
    showNavigationLoading(context, message: loadingMessage);
    
    // Simulate loading time for better UX
    await Future.delayed(delay);
    
    hideNavigationLoading(context);
    navigateTo(route, data: data);
  }
}
